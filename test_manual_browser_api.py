#!/usr/bin/env python3
"""Test the manual browser control API endpoints directly."""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from python.api.manual_browser_control_proxy import ManualBrowserControlProxy
from python.api.manual_browser_control_start import ManualBrowserControlStart
from python.services.browser_session_poc import BrowserSessionPoC
import asyncio
from unittest.mock import Mock

class MockRequest:
    def __init__(self, method='GET', args=None):
        self.method = method
        self.args = args or {}

async def test_browser_session_poc():
    """Test the BrowserSessionPoC directly."""
    print("=" * 60)
    print("Testing BrowserSessionPoC")
    print("=" * 60)
    
    manager = BrowserSessionPoC()
    
    # Test CDP info
    cdp_info = manager.get_cdp_info()
    if cdp_info:
        print(f"✅ CDP Info: {cdp_info.get('Browser', 'Unknown')}")
    else:
        print("❌ CDP not available")
        return False
    
    # Test pages
    pages = manager.get_pages()
    print(f"✅ Found {len(pages)} page(s)")
    
    if pages:
        for i, page in enumerate(pages):
            print(f"   {i+1}. {page.get('title', 'Untitled')} - {page.get('url', '')}")
    
    # Test DevTools URL
    devtools_url = manager.get_devtools_url()
    if devtools_url:
        print(f"✅ DevTools URL: {devtools_url}")
    else:
        print("❌ No DevTools URL available")
    
    return True

async def test_manual_browser_control_start():
    """Test the manual browser control start API."""
    print("\n" + "=" * 60)
    print("Testing ManualBrowserControlStart API")
    print("=" * 60)
    
    api = ManualBrowserControlStart()
    request = MockRequest()
    
    try:
        result = await api.process({}, request)
        print(f"✅ API Response: {result.get('status', 'unknown')}")
        
        if result.get('status') in ['started', 'already_running']:
            print(f"   Message: {result.get('message', '')}")
            print(f"   Pages: {result.get('pages', 0)}")
            if result.get('devtools_url'):
                print(f"   DevTools URL: {result.get('devtools_url')}")
        else:
            print(f"   Error: {result.get('error', 'Unknown error')}")
        
        return result.get('status') in ['started', 'already_running']
        
    except Exception as e:
        print(f"❌ API Error: {e}")
        return False

async def test_manual_browser_control_proxy():
    """Test the manual browser control proxy API."""
    print("\n" + "=" * 60)
    print("Testing ManualBrowserControlProxy API")
    print("=" * 60)
    
    api = ManualBrowserControlProxy()
    
    # Test version endpoint
    request = MockRequest(args={'path': '/json/version'})
    try:
        result = await api.process({}, request)
        if result.get('status') == 'success':
            print(f"✅ Version Proxy: {result['data'].get('Browser', 'Unknown')}")
        else:
            print(f"❌ Version Proxy Error: {result.get('error', 'Unknown')}")
    except Exception as e:
        print(f"❌ Version Proxy Exception: {e}")
    
    # Test pages endpoint
    request = MockRequest(args={'path': '/json'})
    try:
        result = await api.process({}, request)
        if result.get('status') == 'success':
            pages = result['data']
            print(f"✅ Pages Proxy: Found {len(pages)} page(s)")
        else:
            print(f"❌ Pages Proxy Error: {result.get('error', 'Unknown')}")
    except Exception as e:
        print(f"❌ Pages Proxy Exception: {e}")

async def test_page_creation():
    """Test creating a new page via proxy."""
    print("\n" + "=" * 60)
    print("Testing Page Creation via Proxy")
    print("=" * 60)
    
    api = ManualBrowserControlProxy()
    
    # Test creating a new page
    request = MockRequest(method='PUT', args={'path': '/json/new?about:blank'})
    try:
        result = await api.process({}, request)
        if result.get('status') == 'success':
            new_page = result['data']
            print(f"✅ Page Created: {new_page.get('id', 'unknown')}")
            print(f"   URL: {new_page.get('url', '')}")
        else:
            print(f"❌ Page Creation Error: {result.get('error', 'Unknown')}")
    except Exception as e:
        print(f"❌ Page Creation Exception: {e}")

async def main():
    """Run all tests."""
    print("🚀 Manual Browser Control API Tests")
    print("=" * 60)
    
    # Test basic browser session
    if not await test_browser_session_poc():
        print("\n❌ Browser session not available. Make sure Chrome is running with CDP.")
        return
    
    # Test API endpoints
    await test_manual_browser_control_start()
    await test_manual_browser_control_proxy()
    await test_page_creation()
    
    print("\n" + "=" * 60)
    print("✅ All tests completed!")
    print("=" * 60)

if __name__ == "__main__":
    asyncio.run(main())
