#!/usr/bin/env python3
"""
Quick test of browser PoC functionality.
"""

import subprocess
import time
import json
import urllib.request
from pathlib import Path

def kill_existing_chrome():
    """Kill any existing Chrome processes on our CDP port."""
    try:
        subprocess.run(["pkill", "-f", "remote-debugging-port=9222"], capture_output=True)
        time.sleep(2)
    except:
        pass

def start_chrome():
    """Start Chrome with CDP."""
    chrome_path = "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
    data_dir = "/tmp/browser-poc-test"
    
    # Clean data dir
    subprocess.run(["rm", "-rf", data_dir], capture_output=True)
    Path(data_dir).mkdir(parents=True, exist_ok=True)
    
    cmd = [
        chrome_path,
        "--remote-debugging-port=9222",
        f"--user-data-dir={data_dir}",
        "--no-first-run",
        "--disable-default-browser-check",
        "--new-window",
        "https://google.com"
    ]
    
    print("Starting Chrome...")
    process = subprocess.Popen(cmd, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
    time.sleep(5)  # Wait for Chrome to fully start
    return process

def test_cdp():
    """Test CDP connection."""
    try:
        # Test version
        with urllib.request.urlopen("http://localhost:9222/json/version", timeout=10) as response:
            version = json.loads(response.read().decode('utf-8'))
            print(f"✅ CDP connected: {version.get('Browser', 'Unknown')}")
        
        # Test pages
        with urllib.request.urlopen("http://localhost:9222/json", timeout=10) as response:
            pages = json.loads(response.read().decode('utf-8'))
            print(f"✅ Found {len(pages)} pages")
            
            if pages:
                page = pages[0]
                page_id = page.get('id')
                devtools_url = f"http://localhost:9222/devtools/inspector.html?ws=localhost:9222/devtools/page/{page_id}"
                print(f"🔧 DevTools URL: {devtools_url}")
                return devtools_url
                
    except Exception as e:
        print(f"❌ CDP test failed: {e}")
        return None

def main():
    print("Browser PoC Test")
    print("=" * 30)
    
    # Kill existing Chrome
    kill_existing_chrome()
    
    # Start Chrome
    process = start_chrome()
    
    # Test CDP
    devtools_url = test_cdp()
    
    if devtools_url:
        print("\n✅ PoC working! You can now:")
        print(f"1. Open DevTools: {devtools_url}")
        print("2. Connect browser-use to: http://localhost:9222")
        
        input("\nPress Enter to stop Chrome...")
    
    # Clean up
    process.terminate()
    process.wait()
    print("Chrome stopped")

if __name__ == "__main__":
    main()