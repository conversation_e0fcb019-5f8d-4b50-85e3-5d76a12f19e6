# Manual Browser Control - Docker Deployment Guide

## Overview
This PoC needs to work when Agent Zero runs in Docker. Here are the key considerations and implementation approaches:

## Architecture Options for Docker

### Option 1: Browser in Docker Container (Recommended)
Run Chrome inside the Agent Zero Docker container with Xvfb virtual display.

#### Implementation:
```dockerfile
# Add to Agent Zero Dockerfile
RUN apt-get update && apt-get install -y \
    xvfb \
    chromium-browser \
    && rm -rf /var/lib/apt/lists/*

# Start Xvfb and Chrome in entrypoint
ENV DISPLAY=:99
```

#### Startup script:
```bash
# Start virtual display
Xvfb :99 -screen 0 1920x1080x24 &

# Start Chrome with CDP
chromium \
  --remote-debugging-port=9222 \
  --user-data-dir=/tmp/browser-control \
  --no-sandbox \
  --disable-gpu \
  --disable-dev-shm-usage \
  about:blank &
```

#### Advantages:
- ✅ Self-contained - no external dependencies
- ✅ Works in any Docker environment
- ✅ Consistent across deployments
- ✅ No host system requirements

### Option 2: Host Browser Connection
Connect to Chrome running on the host system.

#### Implementation:
```yaml
# docker-compose.yml
version: '3.8'
services:
  agent-zero:
    # ... existing config
    network_mode: "host"  # Access host's localhost:9222
    # OR
    extra_hosts:
      - "host.docker.internal:host-gateway"
```

#### Advantages:
- ✅ Better performance (native Chrome)
- ✅ User can see browser visually
- ❌ Requires user to manually start Chrome with CDP
- ❌ Platform-specific setup

### Option 3: Browserless Service
Use Browserless.io as a separate Docker service.

#### Implementation:
```yaml
version: '3.8'
services:
  browserless:
    image: browserless/chrome:latest
    ports:
      - "3000:3000"
    environment:
      - CONCURRENT=1
      
  agent-zero:
    depends_on:
      - browserless
    environment:
      - BROWSER_CONTROL_URL=ws://browserless:3000
```

## Updated PoC for Docker Support

### Enhanced Browser Session Manager
```python
class BrowserSessionPoC:
    def __init__(self):
        self.cdp_port = 9222
        self.environment = self._detect_environment()
    
    def _detect_environment(self):
        """Detect if running in Docker."""
        if os.path.exists('/.dockerenv'):
            return "docker"
        elif os.environ.get('DOCKER_CONTAINER'):
            return "docker"
        else:
            return "host"
    
    def start_browser(self):
        if self.environment == "docker":
            return self._start_browser_docker()
        else:
            return self._start_browser_host()
    
    def _start_browser_docker(self):
        """Start browser in Docker with Xvfb."""
        # Start Xvfb if not running
        subprocess.run(["Xvfb", ":99", "-screen", "0", "1920x1080x24"], 
                      stdout=subprocess.DEVNULL, 
                      stderr=subprocess.DEVNULL)
        
        # Set display
        os.environ['DISPLAY'] = ':99'
        
        # Start Chromium
        cmd = [
            "chromium",
            f"--remote-debugging-port={self.cdp_port}",
            "--user-data-dir=/tmp/browser-control",
            "--no-sandbox",
            "--disable-gpu",
            "--disable-dev-shm-usage",
            "--no-first-run",
            "about:blank"
        ]
        
        self.browser_process = subprocess.Popen(cmd, ...)
```

### Environment Detection in browser_agent.py
```python
async def _initialize(self):
    # ... existing code ...
    
    # Detect environment and adjust CDP URL
    if os.path.exists('/.dockerenv'):
        # Running in Docker - check internal browser
        test_cdp_url = "http://localhost:9222"
    else:
        # Running on host - could be external browser
        test_cdp_url = "http://localhost:9222"
    
    # Test CDP connection with environment-specific logic
```

## DevTools UI in Docker

### Cross-Origin Considerations
When Agent Zero runs in Docker, the DevTools iframe needs proper CORS headers:

```python
# Add to DevTools server
def do_GET(self):
    # Add CORS headers for Docker deployment
    self.send_header('Access-Control-Allow-Origin', '*')
    self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
    self.send_header('Access-Control-Allow-Headers', '*')
```

### Network Configuration
```yaml
# docker-compose.yml for development
version: '3.8'
services:
  agent-zero:
    ports:
      - "8080:8080"  # Agent Zero UI
      - "9222:9222"  # Chrome CDP
    environment:
      - BROWSER_CONTROL_ENABLED=true
```

## Deployment Recommendations

### Production Deployment
1. **Use Option 1** (Browser in Docker) for production
2. Pre-install Chrome/Chromium in Docker image
3. Start Xvfb and Chrome in container entrypoint
4. Expose CDP port only if needed for debugging

### Development Deployment
1. **Use Option 2** (Host Browser) for development
2. User starts Chrome manually with `--remote-debugging-port=9222`
3. Agent Zero connects to host browser
4. Better debugging experience

### Docker Environment Variables
```bash
# Enable manual browser control
BROWSER_CONTROL_ENABLED=true
BROWSER_CONTROL_CDP_PORT=9222
BROWSER_CONTROL_MODE=docker  # or 'host'
```

## Testing in Docker

### Test Command
```bash
# Test manual browser control in Docker
docker exec agent-zero python3 test_browser_poc.py
```

### Validation Steps
1. ✅ Chrome starts with CDP in Docker container
2. ✅ CDP endpoint accessible at localhost:9222
3. ✅ browser-use can connect to CDP URL
4. ✅ DevTools UI loads in iframe
5. ✅ Manual control works through DevTools

## Next Steps for Docker Integration
1. Update Dockerfile to include Chrome/Xvfb
2. Modify entrypoint script to start browser session
3. Test PoC in Docker environment
4. Update browser session manager for environment detection
5. Validate cross-origin DevTools access