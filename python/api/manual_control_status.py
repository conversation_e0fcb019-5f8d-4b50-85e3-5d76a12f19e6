from flask import Request
from python.helpers.api import <PERSON><PERSON><PERSON><PERSON><PERSON>, Input, Output
import urllib.request
import json
import os

class ManualControlStatus(ApiHandler):
    """Check status of manual browser control services."""
    
    @classmethod
    def get_methods(cls):
        return ["GET", "POST"]
    
    @classmethod
    def requires_csrf(cls) -> bool:
        return False
    
    async def process(self, input: Input, request: Request) -> Output:
        """
        Check if manual browser control services are available.
        Returns status of CDP, VNC, and noVNC services.
        """
        try:
            status = {
                "manual_control_available": False,
                "services": {},
                "urls": {},
                "error": None
            }
            
            # Check if we're in Docker environment
            in_docker = os.path.exists('/.dockerenv') or os.environ.get('DOCKER_CONTAINER')
            status["environment"] = "docker" if in_docker else "host"
            
            # Check CDP (Chrome DevTools Protocol)
            try:
                with urllib.request.urlopen("http://localhost:9222/json/version", timeout=3) as response:
                    cdp_data = json.loads(response.read().decode('utf-8'))
                    status["services"]["cdp"] = {
                        "available": True,
                        "browser": cdp_data.get("Browser", "Unknown"),
                        "version": cdp_data.get("Protocol-Version", "Unknown")
                    }
                    status["urls"]["cdp"] = "http://localhost:9222"
                    
                    # Get pages count
                    try:
                        with urllib.request.urlopen("http://localhost:9222/json", timeout=3) as pages_response:
                            pages_data = json.loads(pages_response.read().decode('utf-8'))
                            status["services"]["cdp"]["pages"] = len(pages_data)
                    except:
                        status["services"]["cdp"]["pages"] = 0
                        
            except Exception as e:
                status["services"]["cdp"] = {
                    "available": False,
                    "error": str(e)
                }
            
            # Check noVNC web interface
            try:
                with urllib.request.urlopen("http://localhost:6080", timeout=3) as response:
                    if response.status == 200:
                        status["services"]["novnc"] = {"available": True}
                        status["urls"]["novnc"] = "http://localhost:6080/vnc.html?autoconnect=true"
                    else:
                        status["services"]["novnc"] = {
                            "available": False,
                            "error": f"HTTP {response.status}"
                        }
            except Exception as e:
                status["services"]["novnc"] = {
                    "available": False,
                    "error": str(e)
                }
            
            # Check VNC direct connection (port check)
            try:
                import socket
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(1)
                result = sock.connect_ex(('localhost', 5900))
                sock.close()
                
                if result == 0:
                    status["services"]["vnc"] = {"available": True}
                    status["urls"]["vnc"] = "localhost:5900"
                else:
                    status["services"]["vnc"] = {
                        "available": False,
                        "error": "Port not accessible"
                    }
            except Exception as e:
                status["services"]["vnc"] = {
                    "available": False,
                    "error": str(e)
                }
            
            # Check API server
            try:
                with urllib.request.urlopen("http://localhost:8000/api/manual_control/status", timeout=3) as response:
                    if response.status == 200:
                        status["services"]["api"] = {"available": True}
                        status["urls"]["api"] = "http://localhost:8000"
                    else:
                        status["services"]["api"] = {
                            "available": False,
                            "error": f"HTTP {response.status}"
                        }
            except Exception as e:
                status["services"]["api"] = {
                    "available": False,
                    "error": str(e)
                }
            
            # Determine overall availability
            cdp_available = status["services"].get("cdp", {}).get("available", False)
            vnc_available = (
                status["services"].get("novnc", {}).get("available", False) or
                status["services"].get("vnc", {}).get("available", False)
            )
            
            status["manual_control_available"] = cdp_available and vnc_available
            
            # Set recommended control method
            if status["services"].get("novnc", {}).get("available", False):
                status["recommended_method"] = "novnc"
                status["control_url"] = status["urls"].get("novnc")
            elif status["services"].get("vnc", {}).get("available", False):
                status["recommended_method"] = "vnc_client"
                status["control_url"] = status["urls"].get("vnc")
            else:
                status["recommended_method"] = "none"
                status["control_url"] = None
            
            # Add integration instructions
            if status["manual_control_available"]:
                status["integration"] = {
                    "browser_use_config": {
                        "cdp_url": status["urls"].get("cdp"),
                        "browser_profile": {
                            "headless": False,
                            "keep_alive": True,
                            "screen": {"width": 1920, "height": 1080},
                            "viewport": {"width": 1920, "height": 1080}
                        }
                    },
                    "manual_control_url": status["control_url"]
                }
            
            return status
            
        except Exception as e:
            return {
                "manual_control_available": False,
                "error": f"Failed to check manual control status: {str(e)}",
                "services": {},
                "urls": {}
            }
