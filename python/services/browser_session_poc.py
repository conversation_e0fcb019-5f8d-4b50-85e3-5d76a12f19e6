#!/usr/bin/env python3
"""
Minimal PoC for browser session management with CDP support.
This allows browser-use to connect to an existing Chrome browser.
"""

import subprocess
import platform
import time
import json
import urllib.request
import os
from typing import Optional, Dict, Any
from pathlib import Path


class BrowserSessionPoC:
    """Minimal browser session manager for CDP connection."""
    
    def __init__(self):
        self.browser_process: Optional[subprocess.Popen] = None
        self.cdp_port = 9222
        self.browser_data_dir = "/tmp/manual-browser-poc"
        self.environment = self._detect_environment()
        
    def _detect_environment(self) -> str:
        """Detect if running in Docker or on host."""
        if os.path.exists('/.dockerenv') or os.environ.get('DOCKER_CONTAINER'):
            return "docker"
        return "host"
        
    def find_chrome(self) -> Optional[str]:
        """Find Chrome executable on the system."""
        system = platform.system()
        
        if system == "Darwin":  # macOS
            paths = [
                "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
                "/Applications/Chromium.app/Contents/MacOS/Chromium",
            ]
        elif system == "Linux":
            if self.environment == "docker":
                # Docker environment - prefer chromium
                paths = [
                    "/usr/bin/chromium",
                    "/usr/bin/chromium-browser", 
                    "/usr/bin/google-chrome",
                    "/opt/google/chrome/chrome",
                ]
            else:
                # Host Linux
                paths = [
                    "/usr/bin/google-chrome",
                    "/usr/bin/chromium",
                    "/usr/bin/chromium-browser",
                ]
        elif system == "Windows":
            paths = [
                "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe",
                "C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe",
            ]
        else:
            return None
            
        for path in paths:
            if Path(path).exists():
                return path
        return None
    
    def start_browser(self) -> bool:
        """Start Chrome with CDP enabled."""
        chrome_path = self.find_chrome()
        if not chrome_path:
            print("Chrome not found on this system")
            return False
        
        # Create data dir
        Path(self.browser_data_dir).mkdir(parents=True, exist_ok=True)
        
        # Chrome launch command
        cmd = [
            chrome_path,
            f"--remote-debugging-port={self.cdp_port}",
            f"--user-data-dir={self.browser_data_dir}",
            "--no-first-run",
            "--disable-default-browser-check",
            "--disable-features=ChromeWhatsNewUI",
            "about:blank"
        ]
        
        # Add platform-specific flags
        if platform.system() == "Linux":
            cmd.extend(["--no-sandbox", "--disable-gpu"])
            
        # Add Docker-specific flags
        if self.environment == "docker":
            cmd.extend([
                "--disable-dev-shm-usage",
                "--disable-extensions",
                "--disable-plugins",
                "--disable-software-rasterizer"
            ])
            
            # Set up Xvfb if needed
            if not os.environ.get('DISPLAY'):
                os.environ['DISPLAY'] = ':99'
                try:
                    subprocess.Popen([
                        "Xvfb", ":99", "-screen", "0", "1920x1080x24"
                    ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
                    time.sleep(1)  # Give Xvfb time to start
                except:
                    print("Warning: Could not start Xvfb - assuming it's already running")
        
        try:
            print(f"Starting Chrome with CDP on port {self.cdp_port}...")
            self.browser_process = subprocess.Popen(
                cmd,
                stdout=subprocess.DEVNULL,
                stderr=subprocess.DEVNULL
            )
            
            # Wait for Chrome to start
            time.sleep(3)
            
            # Check if CDP is available
            if self.get_cdp_info():
                print(f"✅ Chrome started with CDP on http://localhost:{self.cdp_port}")
                return True
            else:
                print("❌ Chrome started but CDP not accessible")
                return False
                
        except Exception as e:
            print(f"Failed to start Chrome: {e}")
            return False
    
    def get_cdp_info(self) -> Optional[Dict[str, Any]]:
        """Get CDP connection info from Chrome."""
        try:
            with urllib.request.urlopen(f"http://localhost:{self.cdp_port}/json/version", timeout=5) as response:
                return json.loads(response.read().decode('utf-8'))
        except Exception as e:
            print(f"CDP connection error: {e}")
            return None
    
    def get_pages(self) -> list:
        """Get list of open pages/tabs."""
        try:
            with urllib.request.urlopen(f"http://localhost:{self.cdp_port}/json", timeout=5) as response:
                return json.loads(response.read().decode('utf-8'))
        except Exception as e:
            print(f"Error getting pages: {e}")
            return []
    
    def get_devtools_url(self) -> Optional[str]:
        """Get DevTools frontend URL for the first page."""
        pages = self.get_pages()
        if pages and len(pages) > 0:
            page = pages[0]
            page_id = page.get('id')
            if page_id:
                return f"http://localhost:{self.cdp_port}/devtools/inspector.html?ws=localhost:{self.cdp_port}/devtools/page/{page_id}"
        return None
    
    def stop_browser(self):
        """Stop the browser process."""
        if self.browser_process:
            self.browser_process.terminate()
            try:
                self.browser_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.browser_process.kill()
            self.browser_process = None
            print("Browser stopped")


def test_browser_use_connection():
    """Test connecting browser-use to external Chrome."""
    try:
        # Import browser-use (may not be available yet)
        from python.helpers.browser_use import browser_use
        
        print("\n📦 Testing browser-use connection...")
        
        # Create browser session with CDP URL
        browser_session = browser_use.BrowserSession(
            cdp_url='http://localhost:9222',
            browser_profile=browser_use.BrowserProfile(
                headless=False,
            )
        )
        
        print("✅ browser-use connected to external Chrome!")
        return browser_session
        
    except ImportError:
        print("⚠️  browser-use not available for testing")
        return None
    except Exception as e:
        print(f"❌ Failed to connect browser-use: {e}")
        return None


def main():
    """Test the browser session PoC."""
    print("=" * 50)
    print("Browser Session PoC - CDP Connection Test")
    print("=" * 50)
    
    manager = BrowserSessionPoC()
    
    # Start browser
    if not manager.start_browser():
        print("Failed to start browser")
        return
    
    # Get CDP info
    info = manager.get_cdp_info()
    if info:
        print(f"\n📊 CDP Info:")
        print(f"  Browser: {info.get('Browser', 'Unknown')}")
        print(f"  Protocol: {info.get('Protocol-Version', 'Unknown')}")
        print(f"  CDP URL: http://localhost:{manager.cdp_port}")
    
    # Get pages
    pages = manager.get_pages()
    print(f"\n📄 Open pages: {len(pages)}")
    for i, page in enumerate(pages):
        print(f"  {i+1}. {page.get('title', 'Untitled')} - {page.get('url', '')}")
    
    # Get DevTools URL
    devtools_url = manager.get_devtools_url()
    if devtools_url:
        print(f"\n🔧 DevTools URL: {devtools_url}")
        print("  (You can open this URL in a browser to access Chrome DevTools)")
    
    # Test browser-use connection
    browser_session = test_browser_use_connection()
    
    print("\n" + "=" * 50)
    print("✅ PoC Test Complete!")
    print("Chrome is running with CDP enabled.")
    print(f"CDP endpoint: http://localhost:{manager.cdp_port}")
    print("=" * 50)
    
    input("\nPress Enter to stop the browser...")
    manager.stop_browser()


if __name__ == "__main__":
    main()