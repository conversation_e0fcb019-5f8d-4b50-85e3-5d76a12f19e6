// Manual Browser Control - Full Implementation with noVNC
import { callJsonApi } from '/js/api.js';

const manualBrowserControlFull = {
    isOpen: false,
    isLoading: false,
    isConnected: false,
    title: 'Manual Browser Control',
    description: 'Take manual control of the browser to handle CAPTCHAs, logins, and interactive elements',
    controlUrl: '',
    apiBaseUrl: 'http://localhost:8000',
    
    async openModal() {
        const modalEl = document.getElementById('manualBrowserControlFullModal');
        
        // If modal doesn't exist, create it
        if (!modalEl) {
            await this.createModal();
        }
        
        const modalAD = Alpine.$data(document.getElementById('manualBrowserControlFullModal'));
        
        modalAD.isOpen = true;
        modalAD.isLoading = true;
        modalAD.title = this.title;
        modalAD.description = this.description;
        
        try {
            // Check manual control status
            console.log('Checking manual browser control status...');
            const statusResponse = await this.checkStatus();
            
            if (statusResponse.status === 'running') {
                this.controlUrl = statusResponse.vnc_url;
                
                modalAD.html = `
                    <div class="manual-control-container" style="width: 100%; height: 700px; position: relative;">
                        <div class="manual-control-header" style="background: #1e1e1e; color: white; padding: 12px; border-radius: 8px 8px 0 0; border-bottom: 1px solid #333;">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div>
                                    <span style="font-weight: bold; color: #4CAF50;">🤖 Agent Zero - Manual Browser Control</span>
                                    <div style="font-size: 12px; opacity: 0.8; margin-top: 2px;">
                                        ${statusResponse.browser?.Browser || 'Chrome'} • ${statusResponse.pages || 0} page(s) open
                                    </div>
                                </div>
                                <div>
                                    <button onclick="document.querySelector('.manual-control-iframe').src = '${this.controlUrl}'" 
                                            style="background: #333; color: white; border: 1px solid #555; padding: 4px 8px; border-radius: 4px; font-size: 11px; cursor: pointer; margin-right: 8px;">
                                        🔄 Refresh
                                    </button>
                                    <button onclick="window.open('${this.controlUrl}', '_blank')" 
                                            style="background: #2196F3; color: white; border: 1px solid #1976D2; padding: 4px 8px; border-radius: 4px; font-size: 11px; cursor: pointer;">
                                        🔗 Open in New Tab
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="manual-control-content" style="height: calc(100% - 60px); position: relative;">
                            <iframe 
                                class="manual-control-iframe"
                                src="${this.controlUrl}" 
                                style="width: 100%; height: 100%; border: none; background: white;"
                                allow="camera; microphone; clipboard-read; clipboard-write; cross-origin-isolated"
                                sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-top-navigation allow-downloads"
                            ></iframe>
                            <div class="manual-control-overlay" style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.1); display: none; align-items: center; justify-content: center; color: white; font-size: 18px;">
                                <div style="background: rgba(0,0,0,0.8); padding: 20px; border-radius: 8px;">
                                    🔄 Reconnecting to browser...
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                
                modalAD.isConnected = true;
                modalAD.isLoading = false;
                
                // Set up iframe error handling
                setTimeout(() => {
                    const iframe = document.querySelector('.manual-control-iframe');
                    if (iframe) {
                        iframe.onerror = () => {
                            console.error('Manual control iframe failed to load');
                            this.showConnectionError(modalAD);
                        };
                    }
                }, 1000);
                
            } else {
                throw new Error(statusResponse.error || 'Manual browser control not available');
            }
            
        } catch (error) {
            console.error('Failed to open manual browser control:', error);
            this.showConnectionError(modalAD, error.message);
        }
    },
    
    async checkStatus() {
        try {
            // Try to check status via API
            const response = await fetch(`${this.apiBaseUrl}/api/manual_control/status`);
            if (response.ok) {
                return await response.json();
            } else {
                throw new Error(`API returned ${response.status}`);
            }
        } catch (error) {
            console.warn('API not available, using fallback status check');
            
            // Fallback: try to check CDP directly
            try {
                const cdpResponse = await fetch('http://localhost:9222/json/version');
                if (cdpResponse.ok) {
                    const cdpData = await cdpResponse.json();
                    return {
                        status: 'running',
                        browser: cdpData,
                        pages: 1,
                        vnc_url: 'http://localhost:6080/vnc.html?autoconnect=true',
                        cdp_url: 'http://localhost:9222'
                    };
                }
            } catch (cdpError) {
                console.error('CDP check failed:', cdpError);
            }
            
            throw new Error('Manual browser control service not available');
        }
    },
    
    showConnectionError(modalAD, errorMessage = 'Connection failed') {
        modalAD.html = `
            <div style="text-align: center; padding: 40px; color: #666;">
                <div style="font-size: 48px; margin-bottom: 20px;">🚫</div>
                <h3 style="color: #e74c3c; margin-bottom: 16px;">Manual Control Not Available</h3>
                <p style="margin-bottom: 20px;">${errorMessage}</p>
                <div style="background: #f8f9fa; padding: 16px; border-radius: 8px; text-align: left; max-width: 500px; margin: 0 auto;">
                    <strong>To enable manual browser control:</strong>
                    <ol style="margin: 8px 0 0 0; padding-left: 20px;">
                        <li>Ensure Docker container is running with manual control enabled</li>
                        <li>Check that ports 6080 (noVNC) and 9222 (CDP) are accessible</li>
                        <li>Verify the manual browser control service is started</li>
                    </ol>
                </div>
                <button onclick="window.manualBrowserControlFull.openModal()" 
                        style="margin-top: 20px; padding: 8px 16px; background: #2196F3; color: white; border: none; border-radius: 4px; cursor: pointer;">
                    🔄 Retry Connection
                </button>
            </div>
        `;
        modalAD.isConnected = false;
        modalAD.isLoading = false;
    },
    
    async createModal() {
        const modalHtml = `
            <div id="manualBrowserControlFullModal" x-data="manualBrowserControlFullModal" x-show="isOpen" 
                 class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" 
                 style="display: none;">
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-6xl w-full mx-4 max-h-[90vh] flex flex-col"
                     @click.away="handleClose()">
                    <div class="flex-shrink-0">
                        <!-- Header -->
                        <div class="flex items-center justify-between p-4 border-b dark:border-gray-700">
                            <div>
                                <h3 class="text-lg font-semibold" x-text="title"></h3>
                                <p class="text-sm text-gray-500 dark:text-gray-400" x-text="description"></p>
                            </div>
                            <button @click="handleClose()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                                <span class="material-symbols-outlined">close</span>
                            </button>
                        </div>
                    </div>
                    
                    <div class="flex-1 min-h-0">
                        <!-- Content -->
                        <div class="h-full p-4">
                            <div x-show="isLoading" class="flex items-center justify-center h-full">
                                <div class="text-center">
                                    <div class="spinner mb-4"></div>
                                    <p class="text-gray-600">Connecting to manual browser control...</p>
                                </div>
                            </div>
                            <div x-show="!isLoading" x-html="html" class="h-full"></div>
                        </div>
                    </div>
                    
                    <div class="flex-shrink-0">
                        <!-- Footer -->
                        <div class="flex justify-between items-center p-4 border-t dark:border-gray-700">
                            <div class="text-sm text-gray-500">
                                <span x-show="isConnected" class="flex items-center">
                                    <span class="material-symbols-outlined text-green-500" style="font-size: 16px; margin-right: 4px;">check_circle</span>
                                    Connected to manual browser control
                                </span>
                                <span x-show="!isConnected && !isLoading" class="flex items-center">
                                    <span class="material-symbols-outlined text-red-500" style="font-size: 16px; margin-right: 4px;">error</span>
                                    Not connected
                                </span>
                            </div>
                            <button @click="handleClose()" class="px-4 py-2 bg-gray-200 dark:bg-gray-700 rounded hover:bg-gray-300 dark:hover:bg-gray-600">
                                Close
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // Add modal to body
        document.body.insertAdjacentHTML('beforeend', modalHtml);
    }
};

// Wait for Alpine to be ready
document.addEventListener('alpine:init', () => {
    Alpine.data('manualBrowserControlFullModal', () => ({
        // Initialize with default values
        isOpen: false,
        isLoading: false,
        isConnected: false,
        title: 'Manual Browser Control',
        description: 'Take manual control of the browser',
        html: '',
        
        init() {
            Object.assign(this, manualBrowserControlFull);
        },
        
        handleClose() {
            this.isOpen = false;
            this.html = '';
            this.isConnected = false;
        }
    }));
});

// Global assignment for access from other modules
window.manualBrowserControlFull = manualBrowserControlFull;

export { manualBrowserControlFull };
