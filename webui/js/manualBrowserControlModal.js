// Manual Browser Control Modal - PoC for Chrome DevTools iframe
import { callJsonApi } from '/js/api.js';

const manualBrowserControlModal = {
    isOpen: false,
    isLoading: false,
    isConnected: false,
    title: 'Manual Browser Control',
    description: 'Take control of the browser to handle CAPTCHAs, logins, and other interactive elements',
    devtoolsUrl: '',
    cdpInfo: null,
    
    async openModal() {
        const modalEl = document.getElementById('manualBrowserControlModal');
        
        // If modal doesn't exist, create it
        if (!modalEl) {
            await this.createModal();
        }
        
        const modalAD = Alpine.$data(document.getElementById('manualBrowserControlModal'));
        
        modalAD.isOpen = true;
        modalAD.isLoading = true;
        modalAD.title = this.title;
        modalAD.description = this.description;
        
        try {
            // Get Chrome version info via proxy
            const versionResponse = await callJsonApi('/manual_browser_control_proxy?path=/json/version', {});
            if (versionResponse.status !== 'success') {
                throw new Error(versionResponse.error || 'Failed to connect to Chrome');
            }

            // Get pages info via proxy
            const pagesResponse = await callJsonApi('/manual_browser_control_proxy?path=/json', {});
            if (pagesResponse.status !== 'success') {
                throw new Error(pagesResponse.error || 'Failed to get Chrome pages');
            }

            const cdpInfo = versionResponse.data;
            const pages = pagesResponse.data;
            
            if (pages && pages.length > 0) {
                const page = pages[0];
                const pageId = page.id;
                // Use localhost since the DevTools iframe will be loaded by the browser on the host
                this.devtoolsUrl = `http://localhost:9222/devtools/inspector.html?ws=localhost:9222/devtools/page/${pageId}`;
                
                modalAD.html = `
                    <div class="browser-control-container" style="width: 100%; height: 700px; position: relative;">
                        <div class="browser-control-header" style="background: #1e1e1e; color: white; padding: 12px; border-radius: 8px 8px 0 0; border-bottom: 1px solid #333;">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div>
                                    <span style="font-weight: bold; color: #4CAF50;">🤖 Agent Zero - Manual Browser Control</span>
                                    <div style="font-size: 12px; opacity: 0.8; margin-top: 2px;">
                                        ${cdpInfo?.Browser || 'Chrome'} • ${pages.length} page(s) open
                                    </div>
                                </div>
                                <div>
                                    <button onclick="document.querySelector('.browser-control-iframe').src = '${this.devtoolsUrl}'" 
                                            style="background: #4CAF50; color: white; border: none; padding: 6px 12px; border-radius: 4px; cursor: pointer; margin-right: 8px; font-size: 12px;">
                                        🔄 Refresh
                                    </button>
                                    <span style="font-size: 11px; opacity: 0.7;">Use DevTools to control the browser manually</span>
                                </div>
                            </div>
                        </div>
                        <iframe 
                            class="browser-control-iframe"
                            src="${this.devtoolsUrl}" 
                            style="width: 100%; height: calc(100% - 60px); border: none; border-radius: 0 0 8px 8px; background: white;"
                            allow="camera; microphone; clipboard-read; clipboard-write; cross-origin-isolated"
                            sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-top-navigation allow-downloads"
                        ></iframe>
                    </div>
                `;
                modalAD.isConnected = true;
                modalAD.isLoading = false;
                
            } else {
                // No pages available, try to create one
                console.log('No pages available, attempting to create a new page...');

                try {
                    // Create a new page using CDP
                    const createResponse = await fetch('http://localhost:9222/json/new?about:blank', {
                        method: 'PUT'
                    });

                    if (createResponse.ok) {
                        const newPage = await createResponse.json();
                        console.log('Created new page:', newPage.id);

                        // Wait a moment for the page to be ready
                        await new Promise(resolve => setTimeout(resolve, 1000));

                        // Try to get pages again
                        const retryResponse = await fetch('http://localhost:9222/json');
                        const retryPages = await retryResponse.json();

                        if (retryPages && retryPages.length > 0) {
                            const page = retryPages[0];
                            this.devtoolsUrl = `http://localhost:9222/devtools/inspector.html?ws=localhost:9222/devtools/page/${page.id}`;

                            modalAD.html = `
                                <div class="browser-control-container" style="width: 100%; height: 700px; position: relative;">
                                    <div class="browser-control-header" style="background: #1e1e1e; color: white; padding: 12px; border-radius: 8px 8px 0 0; border-bottom: 1px solid #333;">
                                        <div style="display: flex; justify-content: space-between; align-items: center;">
                                            <div>
                                                <span style="font-weight: bold; color: #4CAF50;">🤖 Agent Zero - Manual Browser Control</span>
                                                <div style="font-size: 12px; opacity: 0.8; margin-top: 2px;">
                                                    ${cdpInfo?.Browser || 'Chrome'} • ${retryPages.length} page(s) open (created new page)
                                                </div>
                                            </div>
                                            <div>
                                                <button onclick="document.querySelector('.browser-control-iframe').src = '${this.devtoolsUrl}'"
                                                        style="background: #333; color: white; border: 1px solid #555; padding: 4px 8px; border-radius: 4px; font-size: 11px; cursor: pointer;">
                                                    🔄 Refresh
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    <iframe
                                        class="browser-control-iframe"
                                        src="${this.devtoolsUrl}"
                                        style="width: 100%; height: calc(100% - 60px); border: none; border-radius: 0 0 8px 8px; background: white;"
                                        allow="camera; microphone; clipboard-read; clipboard-write; cross-origin-isolated"
                                        sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-top-navigation allow-downloads"
                                    ></iframe>
                                </div>
                            `;
                            modalAD.isConnected = true;
                            modalAD.isLoading = false;
                            return;
                        }
                    }
                } catch (createError) {
                    console.error('Failed to create new page:', createError);
                }

                throw new Error('No browser pages available and failed to create new page');
            }
            
        } catch (error) {
            console.error('Failed to connect to browser:', error);
            modalAD.html = `
                <div style="text-align: center; padding: 40px; color: #666;">
                    <div style="font-size: 48px; margin-bottom: 20px;">🚫</div>
                    <h3 style="color: #e74c3c; margin-bottom: 16px;">Browser Not Available</h3>
                    <p style="margin-bottom: 20px;">No external Chrome browser found with remote debugging enabled.</p>
                    <div style="background: #f8f9fa; padding: 16px; border-radius: 8px; text-align: left; max-width: 500px; margin: 0 auto;">
                        <strong>To enable manual browser control:</strong>
                        <ol style="margin: 8px 0 0 0; padding-left: 20px;">
                            <li>Launch Chrome with: <code>--remote-debugging-port=9222</code></li>
                            <li>Or use the browser control API to start a session</li>
                            <li>Then try opening manual control again</li>
                        </ol>
                    </div>
                </div>
            `;
            modalAD.isLoading = false;
            modalAD.isConnected = false;
        }
    },
    
    async createModal() {
        // Create modal HTML structure
        const modalHtml = `
            <div id="manualBrowserControlModal" x-data="manualBrowserControlModal" x-show="isOpen" 
                 class="fixed inset-0 z-50 overflow-y-auto" style="display: none;"
                 x-cloak>
                <div class="flex items-center justify-center min-h-screen px-4">
                    <!-- Backdrop -->
                    <div class="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
                         @click="handleClose()"></div>
                    
                    <!-- Modal -->
                    <div class="relative bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-7xl w-full mx-auto z-50" style="height: 90vh;">
                        <!-- Header -->
                        <div class="flex items-center justify-between p-4 border-b dark:border-gray-700">
                            <div>
                                <h3 class="text-lg font-semibold" x-text="title"></h3>
                                <p class="text-sm text-gray-500 dark:text-gray-400" x-text="description"></p>
                            </div>
                            <button @click="handleClose()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                                <span class="material-symbols-outlined">close</span>
                            </button>
                        </div>
                        
                        <!-- Content -->
                        <div class="p-4" style="height: calc(100% - 120px);">
                            <div x-show="isLoading" class="flex items-center justify-center h-full">
                                <div class="text-center">
                                    <div class="spinner mb-4"></div>
                                    <p class="text-gray-600">Connecting to browser...</p>
                                </div>
                            </div>
                            <div x-show="!isLoading" x-html="html" style="height: 100%;"></div>
                        </div>
                        
                        <!-- Footer -->
                        <div class="flex justify-between items-center p-4 border-t dark:border-gray-700">
                            <div class="text-sm text-gray-500">
                                <span x-show="isConnected" class="flex items-center">
                                    <span class="material-symbols-outlined text-green-500" style="font-size: 16px; margin-right: 4px;">check_circle</span>
                                    Connected to external browser
                                </span>
                                <span x-show="!isConnected && !isLoading" class="flex items-center">
                                    <span class="material-symbols-outlined text-red-500" style="font-size: 16px; margin-right: 4px;">error</span>
                                    Not connected
                                </span>
                            </div>
                            <button @click="handleClose()" class="px-4 py-2 bg-gray-200 dark:bg-gray-700 rounded hover:bg-gray-300 dark:hover:bg-gray-600">
                                Close
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // Add modal to body
        document.body.insertAdjacentHTML('beforeend', modalHtml);
    }
};

// Wait for Alpine to be ready
document.addEventListener('alpine:init', () => {
    Alpine.data('manualBrowserControlModal', () => ({
        // Initialize with default values
        isOpen: false,
        isLoading: false,
        isConnected: false,
        title: 'Manual Browser Control',
        description: 'Take control of the browser using Chrome DevTools',
        html: '',
        
        init() {
            Object.assign(this, manualBrowserControlModal);
        },
        
        handleClose() {
            this.isOpen = false;
            this.html = '';
        }
    }));
});

// Global assignment for access from other modules
window.manualBrowserControlModal = manualBrowserControlModal;

export { manualBrowserControlModal };