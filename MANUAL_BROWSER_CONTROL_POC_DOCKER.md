# Manual Browser Control PoC - Docker Implementation

## Overview

This <PERSON><PERSON> demonstrates manual browser control for agent-zero using Docker, Xvfb, Chrome, and VNC. It allows browser-use to connect to a headed browser while providing manual control capabilities for handling CAPTCHAs, login forms, and other interactive elements.

## Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    Docker Container                         │
│                                                             │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │
│  │    Xvfb     │  │   Chrome    │  │      x11vnc         │  │
│  │ (Virtual    │  │ (Headed +   │  │   (VNC Server)      │  │
│  │  Display)   │  │    CDP)     │  │                     │  │
│  └─────────────┘  └─────────────┘  └─────────────────────┘  │
│         │                │                      │           │
│         └────────────────┼──────────────────────┘           │
│                          │                                  │
│                          │ CDP Port 9222                    │
│                          │                                  │
└──────────────────────────┼──────────────────────────────────┘
                           │
                           │
┌─────────────────────────────────────────────────────────────┐
│                    Host System                              │
│                                                             │
│  ┌─────────────────┐              ┌─────────────────────┐   │
│  │   browser-use   │              │   VNC Client /      │   │
│  │   (Agent)       │◄─────────────┤   Web Interface     │   │
│  │                 │ CDP          │   (Manual Control)  │   │
│  └─────────────────┘              └─────────────────────┘   │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

## Components

### 1. Xvfb (X Virtual Framebuffer)
- Provides virtual display `:99`
- Runs headless X11 server
- Allows Chrome to run in "headed" mode without physical display

### 2. Chrome with CDP
- Runs on virtual display
- Chrome DevTools Protocol enabled on port 9222
- Accessible to browser-use for automation

### 3. x11vnc
- Captures virtual display
- Provides VNC server on port 5900
- Allows remote control of the browser

### 4. Web Interface
- Simple HTTP server on port 6080
- Provides access information
- Can be extended with noVNC for web-based control

## Files

- `docker_manual_browser_poc.py` - Main PoC implementation
- `Dockerfile.manual-browser` - Docker image definition
- `test_manual_browser_integration.py` - Integration tests
- `build_and_test_poc.sh` - Build and test automation

## Usage

### Quick Start

```bash
# Build and run the PoC
./build_and_test_poc.sh
```

### Manual Steps

```bash
# Build Docker image
docker build -f Dockerfile.manual-browser -t agent-zero-manual-browser:poc .

# Run container
docker run -d \
    --name agent-zero-manual-poc \
    -p 5900:5900 \
    -p 6080:6080 \
    -p 9222:9222 \
    --shm-size=2g \
    agent-zero-manual-browser:poc

# Test CDP connection
curl http://localhost:9222/json/version

# Test browser-use connection
python3 test_manual_browser_integration.py
```

### Manual Control Options

1. **VNC Client**: Connect to `localhost:5900`
2. **Web Interface**: Visit `http://localhost:6080`
3. **CDP Direct**: Use `http://localhost:9222` for debugging

## Integration with browser-use

```python
import browser_use

# Connect to manual browser
browser_session = browser_use.BrowserSession(
    cdp_url="http://localhost:9222",
    browser_profile=browser_use.BrowserProfile(
        headless=False,
        keep_alive=True,
        screen={"width": 1920, "height": 1080},
        viewport={"width": 1920, "height": 1080},
    )
)

await browser_session.start()
# Now browser-use controls the manual browser
```

## Integration with agent-zero

Modify `python/tools/browser_agent.py`:

```python
# In _initialize() method
if manual_control_enabled:
    self.browser_session = browser_use.BrowserSession(
        cdp_url="http://localhost:9222",  # Manual browser
        browser_profile=browser_use.BrowserProfile(headless=False)
    )
```

## Workflow

1. **Automated Phase**: browser-use performs normal automation
2. **Manual Intervention**: When CAPTCHA/login detected:
   - Agent pauses automation
   - Shows manual control modal with VNC iframe
   - User manually handles the challenge
   - Agent resumes automation

## Testing

```bash
# Run all tests
python3 test_manual_browser_integration.py

# Test specific components
curl http://localhost:9222/json/version  # CDP
nc -z localhost 5900                     # VNC
curl http://localhost:6080               # Web interface
```

## Limitations & Next Steps

### Current Limitations
- No authentication for VNC access
- Basic web interface (no embedded VNC)
- Manual process switching

### Production Requirements
1. **Security**: Add VNC authentication
2. **UI Integration**: Embed noVNC in agent-zero web interface
3. **Seamless Switching**: Automatic pause/resume for manual control
4. **Session Management**: Handle multiple concurrent sessions

### Full Implementation Plan
1. Install noVNC in Docker image
2. Create manual control modal in `drawMessageBrowser()`
3. Add API endpoints for session management
4. Integrate with browser_agent.py
5. Add authentication and security

## Troubleshooting

```bash
# Check container logs
docker logs agent-zero-manual-poc

# Check running processes in container
docker exec agent-zero-manual-poc ps aux

# Test VNC connection
vncviewer localhost:5900

# Check CDP endpoints
curl http://localhost:9222/json
```

## Cleanup

```bash
# Stop and remove container
docker stop agent-zero-manual-poc
docker rm agent-zero-manual-poc

# Remove image
docker rmi agent-zero-manual-browser:poc
```

## Conclusion

This PoC demonstrates that manual browser control is feasible using:
- ✅ Docker container with Xvfb + Chrome + VNC
- ✅ browser-use connection via CDP
- ✅ Manual control via VNC
- ✅ Integration path with agent-zero

The foundation is solid for full implementation.
