# Dockerfile for Manual Browser Control - Full Implementation
# Extends the existing agent-zero Docker image with VNC and noVNC capabilities

FROM python:3.11-slim

# Install system dependencies for manual browser control
RUN apt-get update && apt-get install -y \
    # X11 and VNC dependencies
    xvfb \
    x11vnc \
    # Browser dependencies
    chromium \
    chromium-driver \
    # Utilities
    curl \
    wget \
    unzip \
    git \
    # Node.js for noVNC
    nodejs \
    npm \
    # Clean up
    && rm -rf /var/lib/apt/lists/*

# Install noVNC
RUN git clone https://github.com/novnc/noVNC.git /opt/noVNC && \
    git clone https://github.com/novnc/websockify /opt/noVNC/utils/websockify && \
    chmod +x /opt/noVNC/utils/novnc_proxy

# Set environment variables
ENV DISPLAY=:99
ENV DOCKER_CONTAINER=1

# Create working directory
WORKDIR /app

# Copy the full implementation
COPY docker_manual_browser_full.py /app/
COPY python/ /app/python/
COPY webui/ /app/webui/
COPY requirements.txt /app/

# Install Python dependencies
RUN pip install --no-cache-dir \
    browser-use \
    flask \
    flask-basicauth \
    websockets \
    asyncio

# Expose ports
EXPOSE 5900 6080 9222 8000

# Create startup script
RUN echo '#!/bin/bash\n\
echo "🚀 Starting Manual Browser Control - Full Implementation..."\n\
python3 /app/docker_manual_browser_full.py &\n\
echo "⏳ Waiting for services to start..."\n\
sleep 10\n\
echo "✅ Manual Browser Control started. Services available:"\n\
echo "   - noVNC Web: http://localhost:6080/vnc.html"\n\
echo "   - VNC Direct: localhost:5900"\n\
echo "   - CDP: http://localhost:9222"\n\
echo "   - Agent-Zero API: http://localhost:8000"\n\
echo ""\n\
echo "🔗 Manual Control URL: http://localhost:6080/vnc.html?autoconnect=true"\n\
echo ""\n\
tail -f /dev/null\n\
' > /app/start.sh && chmod +x /app/start.sh

CMD ["/app/start.sh"]
