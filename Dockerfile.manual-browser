# Dockerfile for Manual Browser Control PoC
# Extends the existing agent-zero Docker image with VNC capabilities

FROM python:3.11-slim

# Install system dependencies for manual browser control
RUN apt-get update && apt-get install -y \
    # X11 and VNC dependencies
    xvfb \
    x11vnc \
    # Browser dependencies  
    chromium \
    chromium-driver \
    # Utilities
    curl \
    wget \
    unzip \
    # Clean up
    && rm -rf /var/lib/apt/lists/*

# Set environment variables
ENV DISPLAY=:99
ENV DOCKER_CONTAINER=1

# Create working directory
WORKDIR /app

# Copy the PoC script
COPY docker_manual_browser_poc.py /app/
COPY python/ /app/python/

# Install Python dependencies (minimal for PoC)
RUN pip install --no-cache-dir \
    browser-use \
    flask

# Expose ports
EXPOSE 5900 6080 9222 8000

# Create startup script
RUN echo '#!/bin/bash\n\
echo "🚀 Starting Manual Browser Control PoC..."\n\
python3 /app/docker_manual_browser_poc.py &\n\
echo "⏳ Waiting for services to start..."\n\
sleep 5\n\
echo "✅ PoC started. Services available:"\n\
echo "   - VNC: localhost:5900"\n\
echo "   - Web: localhost:6080"\n\
echo "   - CDP: localhost:9222"\n\
echo ""\n\
echo "🔗 Test browser-use connection:"\n\
echo "   python3 -c \"import browser_use; print(browser_use.BrowserSession(cdp_url='\''http://localhost:9222'\''))\""\n\
echo ""\n\
tail -f /dev/null\n\
' > /app/start.sh && chmod +x /app/start.sh

CMD ["/app/start.sh"]
