#!/usr/bin/env python3
"""
Docker Manual Browser Control - Full Implementation
Creates a headed browser in Docker with noVNC web interface for manual control.
"""

import subprocess
import time
import os
import signal
import sys
from pathlib import Path
from typing import Optional, Dict, Any
import urllib.request
import json
import threading

class DockerManualBrowserFull:
    """Full implementation of manual browser control in Docker with noVNC."""
    
    def __init__(self):
        self.display = ":99"
        self.vnc_port = 5900
        self.cdp_port = 9222
        self.novnc_port = 6080
        self.api_port = 8000
        self.browser_data_dir = "/tmp/manual-browser"
        
        # Process handles
        self.xvfb_process: Optional[subprocess.Popen] = None
        self.chrome_process: Optional[subprocess.Popen] = None
        self.vnc_process: Optional[subprocess.Popen] = None
        self.novnc_process: Optional[subprocess.Popen] = None
        self.api_process: Optional[subprocess.Popen] = None
        
        self.is_running = False
        
    def cleanup(self):
        """Clean up all processes."""
        print("🧹 Cleaning up processes...")
        
        processes = [
            ("API Server", self.api_process),
            ("noVNC", self.novnc_process),
            ("VNC", self.vnc_process), 
            ("Chrome", self.chrome_process),
            ("Xvfb", self.xvfb_process)
        ]
        
        for name, process in processes:
            if process:
                try:
                    process.terminate()
                    process.wait(timeout=3)
                    print(f"   ✅ {name} stopped")
                except subprocess.TimeoutExpired:
                    process.kill()
                    print(f"   ⚠️  {name} force killed")
                except Exception as e:
                    print(f"   ❌ Error stopping {name}: {e}")
        
        self.is_running = False
        
    def check_docker_environment(self) -> bool:
        """Check if we're in Docker and have required packages."""
        print("🐳 Checking Docker environment...")
        
        # Check if we're in Docker
        if not (os.path.exists('/.dockerenv') or os.environ.get('DOCKER_CONTAINER')):
            print("   ❌ Not running in Docker container")
            return False
            
        print("   ✅ Running in Docker container")
        
        # Check for required binaries
        required_bins = ["Xvfb", "x11vnc", "chromium"]
        missing = []
        
        for binary in required_bins:
            if not self._find_binary(binary):
                missing.append(binary)
        
        if missing:
            print(f"   ❌ Missing binaries: {', '.join(missing)}")
            return False
            
        # Check for noVNC
        if not Path("/opt/noVNC").exists():
            print("   ❌ noVNC not found at /opt/noVNC")
            return False
            
        print("   ✅ All required components found")
        return True
        
    def _find_binary(self, name: str) -> Optional[str]:
        """Find binary in PATH."""
        try:
            result = subprocess.run(["which", name], capture_output=True, text=True)
            return result.stdout.strip() if result.returncode == 0 else None
        except:
            return None
            
    def start_xvfb(self) -> bool:
        """Start Xvfb virtual display."""
        print(f"🖥️  Starting Xvfb on display {self.display}...")
        
        try:
            self.xvfb_process = subprocess.Popen([
                "Xvfb", self.display,
                "-screen", "0", "1920x1080x24",
                "-ac", "+extension", "GLX",
                "-nolisten", "tcp",
                "-dpi", "96"
            ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            
            time.sleep(2)
            
            if self.xvfb_process.poll() is not None:
                print("   ❌ Xvfb failed to start")
                return False
                
            # Set DISPLAY for all subsequent processes
            os.environ["DISPLAY"] = self.display
            print("   ✅ Xvfb started successfully")
            return True
            
        except Exception as e:
            print(f"   ❌ Failed to start Xvfb: {e}")
            return False
            
    def start_chrome(self) -> bool:
        """Start Chrome with CDP enabled."""
        print(f"🌐 Starting Chrome with CDP on port {self.cdp_port}...")
        
        # Create browser data directory
        Path(self.browser_data_dir).mkdir(parents=True, exist_ok=True)
        
        chrome_cmd = [
            "chromium",
            f"--remote-debugging-port={self.cdp_port}",
            f"--user-data-dir={self.browser_data_dir}",
            "--no-first-run",
            "--no-default-browser-check",
            "--disable-features=ChromeWhatsNewUI",
            "--no-sandbox",
            "--disable-gpu",
            "--disable-dev-shm-usage",
            "--disable-extensions",
            "--disable-plugins",
            "--disable-software-rasterizer",
            "--remote-debugging-address=0.0.0.0",
            "--start-maximized",
            "--window-size=1920,1080",
            "about:blank"
        ]
        
        try:
            self.chrome_process = subprocess.Popen(
                chrome_cmd,
                stdout=subprocess.DEVNULL,
                stderr=subprocess.DEVNULL
            )
            
            time.sleep(3)
            
            if self.chrome_process.poll() is not None:
                print("   ❌ Chrome failed to start")
                return False
                
            # Verify CDP is accessible
            if self._test_cdp():
                print("   ✅ Chrome started with CDP enabled")
                return True
            else:
                print("   ❌ Chrome started but CDP not accessible")
                return False
                
        except Exception as e:
            print(f"   ❌ Failed to start Chrome: {e}")
            return False
            
    def _test_cdp(self) -> bool:
        """Test if CDP is accessible."""
        for attempt in range(10):
            try:
                with urllib.request.urlopen(f"http://localhost:{self.cdp_port}/json/version", timeout=5) as response:
                    data = json.loads(response.read().decode('utf-8'))
                    return "Browser" in data
            except:
                time.sleep(1)
        return False
            
    def start_vnc(self) -> bool:
        """Start x11vnc server."""
        print(f"📺 Starting VNC server on port {self.vnc_port}...")
        
        try:
            self.vnc_process = subprocess.Popen([
                "x11vnc",
                "-display", self.display,
                "-rfbport", str(self.vnc_port),
                "-rfbportv6", str(self.vnc_port),
                "-forever",
                "-nopw",  # No password for Docker environment
                "-shared",
                "-permitfiletransfer",
                "-tightfilexfer",
                "-quiet"
            ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            
            time.sleep(2)
            
            if self.vnc_process.poll() is not None:
                print("   ❌ VNC server failed to start")
                return False
                
            print("   ✅ VNC server started successfully")
            return True
            
        except Exception as e:
            print(f"   ❌ Failed to start VNC server: {e}")
            return False
            
    def start_novnc(self) -> bool:
        """Start noVNC web client."""
        print(f"🌐 Starting noVNC web client on port {self.novnc_port}...")
        
        try:
            self.novnc_process = subprocess.Popen([
                "python3", "/opt/noVNC/utils/novnc_proxy",
                "--vnc", f"localhost:{self.vnc_port}",
                "--listen", str(self.novnc_port),
                "--web", "/opt/noVNC"
            ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            
            time.sleep(3)
            
            if self.novnc_process.poll() is not None:
                print("   ❌ noVNC failed to start")
                return False
                
            print("   ✅ noVNC web client started successfully")
            print(f"   🔗 Access at: http://localhost:{self.novnc_port}/vnc.html")
            return True
            
        except Exception as e:
            print(f"   ❌ Failed to start noVNC: {e}")
            return False
            
    def start_api_server(self) -> bool:
        """Start API server for manual control integration."""
        print(f"🔧 Starting API server on port {self.api_port}...")
        
        try:
            # Create a simple Flask API server
            api_script = f"""
import sys
sys.path.append('/app')
from flask import Flask, jsonify, request
from flask_basicauth import BasicAuth
import json
import urllib.request

app = Flask(__name__)
app.config['BASIC_AUTH_USERNAME'] = 'admin'
app.config['BASIC_AUTH_PASSWORD'] = 'manual-control'
basic_auth = BasicAuth(app)

@app.route('/api/manual_control/status')
def status():
    try:
        with urllib.request.urlopen('http://localhost:{self.cdp_port}/json/version', timeout=5) as response:
            cdp_data = json.loads(response.read().decode('utf-8'))
        
        with urllib.request.urlopen('http://localhost:{self.cdp_port}/json', timeout=5) as response:
            pages_data = json.loads(response.read().decode('utf-8'))
            
        return jsonify({{
            'status': 'running',
            'browser': cdp_data,
            'pages': len(pages_data),
            'vnc_url': 'http://localhost:{self.novnc_port}/vnc.html?autoconnect=true',
            'cdp_url': 'http://localhost:{self.cdp_port}'
        }})
    except Exception as e:
        return jsonify({{'status': 'error', 'error': str(e)}}), 500

@app.route('/api/manual_control/modal')
def modal():
    return '''
    <div style="width: 100%; height: 600px; border: 1px solid #ccc; border-radius: 8px; overflow: hidden;">
        <iframe src="http://localhost:{self.novnc_port}/vnc.html?autoconnect=true&resize=scale" 
                style="width: 100%; height: 100%; border: none;">
        </iframe>
    </div>
    '''

if __name__ == '__main__':
    app.run(host='0.0.0.0', port={self.api_port}, debug=False)
"""
            
            with open("/tmp/api_server.py", "w") as f:
                f.write(api_script)
            
            self.api_process = subprocess.Popen([
                "python3", "/tmp/api_server.py"
            ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            
            time.sleep(2)
            
            if self.api_process.poll() is not None:
                print("   ❌ API server failed to start")
                return False
                
            print("   ✅ API server started successfully")
            return True
            
        except Exception as e:
            print(f"   ❌ Failed to start API server: {e}")
            return False
            
    def start_all(self) -> bool:
        """Start all components."""
        print("🚀 Starting Manual Browser Control - Full Implementation...")
        print("=" * 70)
        
        if not self.check_docker_environment():
            return False
            
        # Start components in order
        if not self.start_xvfb():
            self.cleanup()
            return False
            
        if not self.start_chrome():
            self.cleanup()
            return False
            
        if not self.start_vnc():
            self.cleanup()
            return False
            
        if not self.start_novnc():
            self.cleanup()
            return False
            
        if not self.start_api_server():
            self.cleanup()
            return False
            
        self.is_running = True
        
        print("\n" + "=" * 70)
        print("✅ Manual Browser Control - Full Implementation Running!")
        print("=" * 70)
        print(f"🌐 Manual Control: http://localhost:{self.novnc_port}/vnc.html?autoconnect=true")
        print(f"📺 VNC Direct: localhost:{self.vnc_port}")
        print(f"🔧 CDP Endpoint: http://localhost:{self.cdp_port}")
        print(f"🔌 API Server: http://localhost:{self.api_port}")
        print("=" * 70)
        print("🎯 Integration URLs:")
        print(f"   Status: http://localhost:{self.api_port}/api/manual_control/status")
        print(f"   Modal:  http://localhost:{self.api_port}/api/manual_control/modal")
        print("=" * 70)
        
        return True

def main():
    """Main function for full implementation."""
    browser_control = DockerManualBrowserFull()
    
    def signal_handler(sig, frame):
        print("\n🛑 Received interrupt signal")
        browser_control.cleanup()
        sys.exit(0)
        
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    if browser_control.start_all():
        print("\n⏳ Full implementation running... Press Ctrl+C to stop")
        try:
            while browser_control.is_running:
                time.sleep(1)
        except KeyboardInterrupt:
            pass
    
    browser_control.cleanup()

if __name__ == "__main__":
    main()
