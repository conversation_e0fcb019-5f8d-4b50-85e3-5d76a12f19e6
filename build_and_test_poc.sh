#!/bin/bash

# Manual Browser Control - Full Implementation Build and Test Script

set -e

echo "🚀 Manual Browser Control - Full Implementation Build and Test"
echo "================================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is available
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed or not in PATH"
    exit 1
fi

print_success "Docker is available"

# Build the Docker image
print_status "Building Docker image for full manual browser control..."
docker build -f Dockerfile.manual-browser -t agent-zero-manual-browser:full .

if [ $? -eq 0 ]; then
    print_success "Docker image built successfully"
else
    print_error "Failed to build Docker image"
    exit 1
fi

# Stop any existing container
print_status "Stopping any existing containers..."
docker stop agent-zero-manual-full 2>/dev/null || true
docker rm agent-zero-manual-full 2>/dev/null || true

# Run the container
print_status "Starting full manual browser control container..."
docker run -d \
    --name agent-zero-manual-full \
    -p 5900:5900 \
    -p 6080:6080 \
    -p 9222:9222 \
    -p 8000:8000 \
    --shm-size=2g \
    agent-zero-manual-browser:full

if [ $? -eq 0 ]; then
    print_success "Container started successfully"
else
    print_error "Failed to start container"
    exit 1
fi

# Wait for services to start
print_status "Waiting for services to start..."
sleep 10

# Check if services are running
print_status "Checking service status..."

# Check CDP endpoint
if curl -s http://localhost:9222/json/version > /dev/null; then
    print_success "CDP endpoint is accessible"
else
    print_warning "CDP endpoint not yet accessible, waiting..."
    sleep 5
    if curl -s http://localhost:9222/json/version > /dev/null; then
        print_success "CDP endpoint is now accessible"
    else
        print_error "CDP endpoint failed to start"
        docker logs agent-zero-manual-poc
        exit 1
    fi
fi

# Check VNC port
if nc -z localhost 5900 2>/dev/null; then
    print_success "VNC server is running"
else
    print_warning "VNC server may not be running"
fi

# Check web interface
if curl -s http://localhost:6080 > /dev/null; then
    print_success "Web interface is accessible"
else
    print_warning "Web interface may not be accessible"
fi

# Run comprehensive tests
print_status "Running full implementation tests..."
python3 test_full_manual_control.py

if [ $? -eq 0 ]; then
    print_success "All tests passed"
else
    print_error "Some tests failed"
    print_status "Container logs:"
    docker logs agent-zero-manual-full
    exit 1
fi

echo ""
echo "================================================================"
print_success "Manual Browser Control - Full Implementation is running!"
echo "================================================================"
echo "🌐 Manual Control (noVNC): http://localhost:6080/vnc.html?autoconnect=true"
echo "📺 VNC Direct: localhost:5900"
echo "🔧 CDP Endpoint: http://localhost:9222"
echo "🔌 API Server: http://localhost:8000"
echo "📋 Container: agent-zero-manual-full"
echo ""
echo "🎯 Key URLs:"
echo "  # Manual Control Interface"
echo "  open http://localhost:6080/vnc.html?autoconnect=true"
echo ""
echo "  # API Status Check"
echo "  curl http://localhost:8000/api/manual_control/status"
echo ""
echo "  # CDP Browser Info"
echo "  curl http://localhost:9222/json/version"
echo ""
echo "🧪 Test Commands:"
echo "  # Run full test suite"
echo "  python3 test_full_manual_control.py"
echo ""
echo "  # View container logs"
echo "  docker logs agent-zero-manual-full"
echo ""
echo "  # Connect with VNC client"
echo "  vncviewer localhost:5900"
echo ""
echo "🛑 To stop:"
echo "  docker stop agent-zero-manual-full"
echo "================================================================"
