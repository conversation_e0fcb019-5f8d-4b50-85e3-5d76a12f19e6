<!DOCTYPE html>
<html>
<head>
    <title>Test Manual Browser Control</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        button { padding: 10px 20px; margin: 10px; font-size: 16px; }
        .success { background-color: #4CAF50; color: white; }
        .error { background-color: #f44336; color: white; }
        #result { margin-top: 20px; padding: 10px; border-radius: 4px; }
    </style>
</head>
<body>
    <h1>Manual Browser Control Test</h1>
    
    <button onclick="testBrowserStatus()">Test Browser Status</button>
    <button onclick="testCreatePage()">Test Create Page</button>
    <button onclick="testManualControl()">Test Manual Control Modal</button>
    
    <div id="result"></div>

    <script>
        async function testBrowserStatus() {
            const result = document.getElementById('result');
            try {
                const response = await fetch('http://localhost:9222/json');
                const pages = await response.json();
                
                result.className = 'success';
                result.innerHTML = `
                    <h3>✅ Browser Status: OK</h3>
                    <p>Found ${pages.length} page(s)</p>
                    <pre>${JSON.stringify(pages, null, 2)}</pre>
                `;
            } catch (error) {
                result.className = 'error';
                result.innerHTML = `
                    <h3>❌ Browser Status: Error</h3>
                    <p>${error.message}</p>
                `;
            }
        }

        async function testCreatePage() {
            const result = document.getElementById('result');
            try {
                const response = await fetch('http://localhost:9222/json/new?about:blank', {
                    method: 'PUT'
                });
                const newPage = await response.json();
                
                result.className = 'success';
                result.innerHTML = `
                    <h3>✅ Page Created Successfully</h3>
                    <p>New page ID: ${newPage.id}</p>
                    <pre>${JSON.stringify(newPage, null, 2)}</pre>
                `;
            } catch (error) {
                result.className = 'error';
                result.innerHTML = `
                    <h3>❌ Create Page: Error</h3>
                    <p>${error.message}</p>
                `;
            }
        }

        async function testManualControl() {
            const result = document.getElementById('result');
            try {
                // Get pages
                const pagesResponse = await fetch('http://localhost:9222/json');
                const pages = await pagesResponse.json();
                
                if (pages.length === 0) {
                    throw new Error('No pages available');
                }
                
                const page = pages[0];
                const devtoolsUrl = `http://localhost:9222/devtools/inspector.html?ws=localhost:9222/devtools/page/${page.id}`;
                
                result.className = 'success';
                result.innerHTML = `
                    <h3>✅ Manual Control Ready</h3>
                    <p>DevTools URL: <a href="${devtoolsUrl}" target="_blank">${devtoolsUrl}</a></p>
                    <p>Page: ${page.title || 'Untitled'} (${page.url})</p>
                    <button onclick="window.open('${devtoolsUrl}', '_blank')">Open DevTools</button>
                `;
            } catch (error) {
                result.className = 'error';
                result.innerHTML = `
                    <h3>❌ Manual Control: Error</h3>
                    <p>${error.message}</p>
                `;
            }
        }

        // Auto-test on load
        window.onload = () => {
            testBrowserStatus();
        };
    </script>
</body>
</html>
