#!/usr/bin/env python3
"""Simple script to start browser for testing manual control."""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from python.services.browser_session_poc import BrowserSessionPoC
import time

def main():
    print("Starting browser for manual control testing...")
    
    manager = BrowserSessionPoC()
    
    # Start browser
    if not manager.start_browser():
        print("Failed to start browser")
        return
    
    # Get CDP info
    info = manager.get_cdp_info()
    if info:
        print(f"✅ Browser: {info.get('Browser', 'Unknown')}")
        print(f"✅ CDP URL: http://localhost:{manager.cdp_port}")
    
    # Get pages
    pages = manager.get_pages()
    print(f"✅ Found {len(pages)} page(s) available")
    
    # Get DevTools URL
    devtools_url = manager.get_devtools_url()
    if devtools_url:
        print(f"✅ DevTools URL: {devtools_url}")
    
    print("\n🚀 Browser is ready for manual control!")
    print("Press Ctrl+C to stop...")
    
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\nStopping browser...")
        manager.stop_browser()
        print("Browser stopped.")

if __name__ == "__main__":
    main()
